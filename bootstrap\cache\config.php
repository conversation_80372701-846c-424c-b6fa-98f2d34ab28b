<?php return array (
  4 => 'concurrency',
  5 => 'cors',
  8 => 'hashing',
  14 => 'view',
  'app' => 
  array (
    'name' => 'Laravel',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:s4CnF8WwEEN1OSpVdhWEQSy3xsRNCfmfPOfAiF0cpmc=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
      6 => 'Illuminate\\Cookie\\CookieServiceProvider',
      7 => 'Illuminate\\Database\\DatabaseServiceProvider',
      8 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      9 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      10 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      11 => 'Illuminate\\Hashing\\HashServiceProvider',
      12 => 'Illuminate\\Mail\\MailServiceProvider',
      13 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      14 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      15 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      16 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      17 => 'Illuminate\\Queue\\QueueServiceProvider',
      18 => 'Illuminate\\Redis\\RedisServiceProvider',
      19 => 'Illuminate\\Session\\SessionServiceProvider',
      20 => 'Illuminate\\Translation\\TranslationServiceProvider',
      21 => 'Illuminate\\Validation\\ValidationServiceProvider',
      22 => 'Illuminate\\View\\ViewServiceProvider',
      23 => 'App\\Providers\\AppServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Concurrency' => 'Illuminate\\Support\\Facades\\Concurrency',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Context' => 'Illuminate\\Support\\Facades\\Context',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schedule' => 'Illuminate\\Support\\Facades\\Schedule',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Uri' => 'Illuminate\\Support\\Uri',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'database',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'cache',
        'lock_connection' => NULL,
        'lock_table' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\framework/cache/data',
        'lock_path' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'laravel_cache_',
  ),
  'constant' => 
  array (
    'default_password_self_portal' => 'P@ssword1234',
    'email_sender' => '<EMAIL>',
    'email_sender_name' => 'Pentadbir',
    'email_sender_epss' => '<EMAIL>',
    'email_sender_name_epss' => 'eP Auto Notify',
    'mail_ep_host_casb' => 'smtp.office365.com',
    'mail_ep_port_casb' => '587',
    'mail_ep_encryption_casb' => 'TLS',
    'mail_ep_username_casb' => '<EMAIL>',
    'mail_ep_password_casb' => 'Casb@!@#$%^&*',
    'email_sender_casb' => '<EMAIL>',
    'email_sender_name_casb' => 'CASB DO NOT REPLY',
    'roles_adv_ep' => 
    array (
      0 => 'Group IT Coordinator',
      1 => 'Group Middleware',
      2 => 'Group IT Specialist(Production Support)',
      3 => 'Group IT Specialist(Developer)',
      4 => 'Approver',
      5 => 'Group Archisoft Build Team',
      6 => 'Group IT Specialist(Database Admin)',
      7 => 'Group IT Specialist',
      8 => 'Group IT Specialist(Network Admin)',
    ),
    'users_dev_ep' => 
    array (
      0 => 'iqbalfikri',
      1 => 'mohdshamsul',
      2 => 'shahril',
      3 => 'hazman',
      4 => 'lilian2',
      5 => 'sharudin',
      6 => 'lilian',
    ),
    'roles_patch_ep' => 
    array (
      0 => 'Group Middleware',
      1 => 'Group IT Specialist(Production Support)',
    ),
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'oracle' => 
      array (
        'driver' => 'oracle',
        'tns' => '',
        'host' => '***************',
        'port' => '3306',
        'database' => 'cdccrm_stg',
        'username' => 'crm_user',
        'password' => 'cDcCrmDev@2024',
        'charset' => 'AL32UTF8',
        'prefix' => '',
        'prefix_schema' => '',
      ),
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'cdccrm_stg',
        'prefix' => '',
        'foreign_key_constraints' => true,
        'busy_timeout' => NULL,
        'journal_mode' => NULL,
        'synchronous' => NULL,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'host' => '***************',
        'port' => '3306',
        'database' => 'cdccrm_stg',
        'username' => 'crm_user',
        'password' => 'cDcCrmDev@2024',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '***************',
        'port' => '3306',
        'database' => 'cdccrm_stg',
        'username' => 'crm_user',
        'password' => 'cDcCrmDev@2024',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'host' => '***************',
        'port' => '3306',
        'database' => 'cdccrm_stg',
        'username' => 'crm_user',
        'password' => 'cDcCrmDev@2024',
        'charset' => 'utf8',
        'prefix' => '',
        'schema' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'host' => '************',
        'port' => '49430',
        'database' => 'Ulysses7i_CDC',
        'username' => 'midware',
        'password' => 'midware123',
        'prefix' => '',
      ),
      'mysql_crm' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'cdccrm_stg',
        'username' => 'crm_user',
        'password' => 'cDcCrmDev@2024',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_archieve' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'cdccrm',
        'username' => 'suppcrm',
        'password' => 'Suppcrm@2017',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_integ' => 
      array (
        'driver' => 'mysql',
        'host' => 'localhost',
        'port' => '3306',
        'database' => 'crm_integration',
        'username' => 'root',
        'password' => 'root@123',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
      ),
      'mysql_invoice_migration' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'invoice_migrate',
        'username' => 'crm_user',
        'password' => 'cDccRm@2017',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
      ),
      'mysql_ep_support' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'ep_support',
        'username' => 'epss_user',
        'password' => 'cDcePss@us2024',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
      ),
      'sqlsrv_cms' => 
      array (
        'driver' => 'sqlsrv',
        'host' => '*************',
        'port' => '1433',
        'database' => 'epprod',
        'username' => 'cms_read',
        'password' => 'cm5_r34d',
        'prefix' => '',
      ),
      'sqlsrv_aspect' => 
      array (
        'driver' => 'sqlsrv',
        'host' => '************',
        'port' => '1433',
        'database' => 'detail_epro',
        'username' => 'dbusr',
        'password' => 'Casb@1234',
        'prefix' => '',
      ),
      'oracle_epcore_pms' => 
      array (
        'driver' => 'oracle',
        'tns' => '',
        'host' => '*************',
        'port' => '1521',
        'database' => 'EPCORE.COMMERCEDC.COM.MY',
        'service_name' => 'EPCORE.COMMERCEDC.COM.MY',
        'username' => 'pms',
        'password' => 'pms',
        'charset' => 'AL32UTF8',
        'prefix' => '',
      ),
      'oracle_nextgen' => 
      array (
        'driver' => 'oracle',
        'tns' => '',
        'host' => 'rac-cluster-scan.eperolehan.com.my',
        'port' => '1521',
        'database' => 'uatapp',
        'service_name' => 'uatapp',
        'username' => 'ngep_uat',
        'password' => 'ng3p_u4t',
        'charset' => 'AL32UTF8',
        'prefix' => '',
      ),
      'oracle_nextgen_rpt' => 
      array (
        'driver' => 'oracle',
        'tns' => '',
        'host' => 'rac-cluster-scan.eperolehan.com.my',
        'port' => '1521',
        'database' => 'uatapp',
        'service_name' => 'uatapp',
        'username' => 'ngep_uat',
        'password' => 'ng3p_u4t',
        'charset' => 'AL32UTF8',
        'prefix' => '',
      ),
      'oracle_nextgen_fullgrant' => 
      array (
        'driver' => 'oracle',
        'tns' => '',
        'host' => 'rac-cluster-scan.eperolehan.com.my',
        'port' => '1521',
        'database' => 'pdsapp',
        'service_name' => 'pdsapp',
        'username' => 'ngep_pds',
        'password' => 'ng3p_pd5',
        'charset' => 'AL32UTF8',
        'prefix' => '',
      ),
      'oracle_bpm_rpt' => 
      array (
        'driver' => 'oracle',
        'tns' => '',
        'host' => 'racrpt-cluster-scan.eperolehan.com.my',
        'port' => '1521',
        'database' => 'pdssoa',
        'service_name' => 'pdssoa',
        'username' => 'pdssoa_soainfra',
        'password' => 'pd5504',
        'charset' => 'AL32UTF8',
        'prefix' => '',
      ),
      'mysql_casb' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'casbcrm',
        'username' => 'crm_user',
        'password' => 'CasbCrm@2022',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_casb_migration' => 
      array (
        'driver' => 'mysql',
        'host' => '***************',
        'port' => '3306',
        'database' => 'migration',
        'username' => 'crm_user',
        'password' => 'cRm@2017',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_crm_ssm' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'crm_ssm',
        'username' => 'crm_user',
        'password' => 'CasbCrm@2022',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_crm_ssm_dev' => 
      array (
        'driver' => 'mysql',
        'host' => '***************',
        'port' => '3306',
        'database' => 'casbcrm_ssm_dev',
        'username' => 'crm_user',
        'password' => 'cRm@2017',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_crm_ssm_migration' => 
      array (
        'driver' => 'mysql',
        'host' => '***************',
        'port' => '3306',
        'database' => 'migration',
        'username' => 'crm_user',
        'password' => 'cRm@2017',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_crm_jbal' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'casbcrm_jbal',
        'username' => 'crm_user',
        'password' => 'CasbCrm@2022',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_ep_notify' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'ep_notify',
        'username' => 'epssadmin',
        'password' => 'cDc@2018',
        'charset' => 'utf8',
        'collation' => 'utf8_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_crm_gamuda' => 
      array (
        'driver' => 'mysql',
        'host' => '**************',
        'port' => '3306',
        'database' => 'crm_gamuda',
        'username' => 'crm_user',
        'password' => 'CasbCrm@2022',
        'charset' => 'latin1',
        'collation' => 'latin1_swedish_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
      'mysql_crm_vantage' => 
      array (
        'driver' => 'mysql',
        'host' => '***************',
        'port' => '3306',
        'database' => 'crm_vantage',
        'username' => 'crm_user',
        'password' => 'cRm@2017',
        'charset' => 'latin1',
        'collation' => 'latin1_swedish_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => NULL,
        'modes' => 
        array (
          0 => 'ONLY_FULL_GROUP_BY',
          1 => 'STRICT_TRANS_TABLES',
          2 => 'NO_ZERO_IN_DATE',
          3 => 'NO_ZERO_DATE',
          4 => 'ERROR_FOR_DIVISION_BY_ZERO',
          5 => 'NO_ENGINE_SUBSTITUTION',
        ),
      ),
    ),
    'migrations' => 
    array (
      'table' => 'migrations',
      'update_date_on_publish' => true,
    ),
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'laravel_database_',
        'persistent' => false,
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\app/private',
        'serve' => true,
        'throw' => false,
        'report' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\app/public',
        'url' => 'http://localhost/storage',
        'visibility' => 'public',
        'throw' => false,
        'report' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
    ),
    'links' => 
    array (
      'E:\\workspace\\cdccrm-integration-upgrade\\public\\storage' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\app/public',
    ),
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'deprecations' => 
    array (
      'channel' => NULL,
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'handler_with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'formatter' => NULL,
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'log',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'scheme' => NULL,
        'url' => NULL,
        'host' => 'smtp.office365.com',
        'port' => '587',
        'username' => '<EMAIL>',
        'password' => 'Cdc@12345678',
        'timeout' => NULL,
        'local_domain' => 'localhost',
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
        'retry_after' => 60,
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
        'retry_after' => 60,
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Example',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'E:\\workspace\\cdccrm-integration-upgrade\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'oracle' => 
  array (
    'oracle' => 
    array (
      'driver' => 'oracle',
      'tns' => '',
      'host' => '***************',
      'port' => '3306',
      'database' => 'cdccrm_stg',
      'username' => 'crm_user',
      'password' => 'cDcCrmDev@2024',
      'charset' => 'AL32UTF8',
      'prefix' => '',
      'prefix_schema' => '',
    ),
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'batching' => 
    array (
      'database' => 'mysql',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'remote' => 
  array (
    'default' => 'production',
    'connections' => 
    array (
      'osb' => 
      array (
        'host' => '*************',
        'username' => 'support',
        'password' => '5uPP0rt!@#',
        'key' => '',
        'keytext' => '',
        'keyphrase' => '',
        'agent' => '',
        'timeout' => 15,
      ),
      'portal' => 
      array (
        'host' => '*************',
        'username' => 'support',
        'password' => '5uPP0rt!@#',
        'key' => '',
        'keytext' => '',
        'keyphrase' => '',
        'agent' => '',
        'timeout' => 30,
      ),
      'crm-ep' => 
      array (
        'host' => '**************',
        'username' => 'suppcrm',
        'password' => 'cDc@2022',
        'key' => '',
        'keytext' => '',
        'keyphrase' => '',
        'agent' => '',
        'timeout' => 30,
      ),
      'crm-ssm' => 
      array (
        'host' => '**************',
        'username' => 'suppcrm',
        'password' => 'cRm@2017',
        'key' => '',
        'keytext' => '',
        'keyphrase' => '',
        'agent' => '',
        'timeout' => 30,
      ),
    ),
    'groups' => 
    array (
      'web' => 
      array (
        0 => 'production',
      ),
    ),
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => 120,
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'laravel_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
  ),
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => '12',
      'verify' => true,
      'limit' => NULL,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
      'verify' => true,
    ),
    'rehash_on_login' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'E:\\workspace\\cdccrm-integration-upgrade\\resources\\views',
    ),
    'compiled' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\framework\\views',
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => NULL,
    'convert_entities' => true,
    'options' => 
    array (
      'font_dir' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\fonts',
      'font_cache' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'E:\\workspace\\cdccrm-integration-upgrade',
      'allowed_protocols' => 
      array (
        'data://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'artifactPathValidation' => NULL,
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => false,
      'allowed_remote_hosts' => NULL,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
        'test_auto_detect' => true,
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => false,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'guess',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
      'cells' => 
      array (
        'middleware' => 
        array (
        ),
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => 'E:\\workspace\\cdccrm-integration-upgrade\\storage\\framework/cache/laravel-excel',
      'local_permissions' => 
      array (
      ),
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => NULL,
    ),
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
