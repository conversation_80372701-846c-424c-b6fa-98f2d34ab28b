<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        // Commands\PmsIntegrationToCrmSchedule::class,
        Commands\NextgenToGovernmentCRMSchedule::class,
        Commands\NextgenToGovernmentUsersCRMSchedule::class,
        Commands\NextgenToSupplierCRMSchedule::class,
        Commands\NextgenToSupplierUsersCRMSchedule::class,

        // Commands\DataFixedCrm::class,
        Commands\UpdateSLASchedule::class,
        Commands\ReportCasesResolved::class,
        Commands\CheckCaseStatusTaskMissingSchedule::class,
        Commands\CaseDetailIncidentServiceSchedule::class,
        Commands\AutocloseCaseSchedule::class,
        Commands\ReportCasesAvgOpenPortal::class,
        Commands\ReportCasesAvgOpenPortalDaily::class,
        Commands\ReportCasesAvgOpenPortalMonthly::class,
        Commands\CaseDetailEnquirySchedule::class,
        Commands\CheckEmailInboundSchedule::class,
        Commands\DuplicateCaseSchedule::class,
        Commands\UpdateCSSLASchedule::class,
        Commands\CaseDetailAllSchedule::class,
        Commands\CaseDetailAllDailySchedule::class,
        Commands\CaseDetailAllMonthlySchedule::class,
        // Commands\CaseDetailOpenPortalEmailSchedule::class,
        Commands\statisticPerformanceAgentReportSchedule::class,
        Commands\DailyCollectionCdciReportSchedule::class,
        Commands\ReportStatisticSupplierCasesMonitoring::class,
        Commands\statisticPerformanceOutboundReportSchedule::class,
        Commands\LmsTrainingCertGeneratorSchedule::class,
        Commands\LmsTrainingNotesSchedule::class,
        Commands\UpdateCaseStatusSchedule::class,
        Commands\StatisticPendingAgeingCaseIncidentReportSchedule::class,
        Commands\CheckAssignedCaseNoPendingTaskSchedule::class,
        Commands\CaseDetailIncidentServiceMonthlySchedule::class,
        Commands\PomSlaManagementSchedule::class,
        Commands\CaseRejectedEaduanSchedule::class,

        Commands\UpdateSLAFlag3Schedule::class,
        Commands\AutoAckSeveritySchedule::class,
        Commands\CheckCaseS3::class,
        Commands\CaseDetailOnsiteSupportScheduleDaily::class,
        Commands\CaseDetailOnsiteSupportScheduleToday::class,
        // Commands\AutoCreateTaskRITSchedule::class,
        Commands\InvalidCaseSchedule::class,
        Commands\AutoApprovals4Scheduler::class,

        Commands\AspectDailyCallDetail::class,
        Commands\CheckCaseExecutionDateSchedule::class,
        Commands\CheckBugsCaseSchedule::class,
        Commands\CaseDetailAllAdhocSchedule::class,
        Commands\AutoFillCategoryCaseCancelledEaduanSchedule::class,
        
        /*
         * CASB Report
         */
        Commands\MyTV_Altel\ReportCaseDetails::class,
        Commands\MyTV_Altel\ReportCaseDetailsWeekly::class,
        Commands\MyTV_Altel\ReportCaseDetailsMonthly::class,
        
        /*
         * Crm Ssm Report
         */
        Commands\CrmSsm\ReportCaseDetails::class,
        Commands\CrmSsm\ReportCaseDetailsWeekly::class,
        Commands\CrmSsm\ReportCaseDetailsMonthly::class,
        Commands\CrmSsm\ReportOpenCaseDetails::class,
        Commands\CrmSsm\ReportDailyOpenCaseDetails::class,
        
        
        Commands\CrmSsm\DeleteInvalidCaseSchedule::class,
        Commands\CrmSsm\CheckEmailInboundSchedule::class,
        Commands\CrmSsm\DuplicateCaseSchedule::class,
        Commands\CrmSsm\DeleteEmptyDescCaseSchedule::class,
        Commands\CrmSsm\MissingTaskSchedule::class,
        Commands\CrmSsm\OpenInvalidCaseSchedule::class,
        
        Commands\CrmJbal\ReportCaseDetailsWeekly::class,
        Commands\CrmJbal\ReportCaseDetailsDaily::class,
        Commands\CrmSsm\EmailListenerSchedule::class,
        
        Commands\ReportMonitoringSchedule::class,

        // Crm Gamuda Report
        Commands\CrmGamuda\ReportCaseDetails::class,

    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        /* Schedule for CRM - Nextgen */
        $schedule->command('integrate-nextgen-government')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('integrate-nextgen-government-users')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('integrate-nextgen-supplier')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('integrate-nextgen-supplier-users')->everyFiveMinutes()->unlessBetween('04:00', '06:00');

        /* Schedule for Aspect */
        // $schedule->command('aspect-daily-call-detail')->dailyAt('08:25'); -- discontinued 2023-10 - use talkdesk instead

        /* Schedule send report CRM */
        $schedule->command('crm-cases-resolved')->dailyAt('06:10');
        $schedule->command('case-detail-incident-service')->dailyAt('06:15');
        $schedule->command('case-detail-enquiry')->monthlyOn(1, '06:30');
        $schedule->command('case-detail-daily')->dailyAt('17:30');
        $schedule->command('case-detail-monthly')->monthlyOn(1, '03:00');
        $schedule->command('case-detail-incident-service-monthly')->monthlyOn(1, '7.00');
        $schedule->command('case-rejected-eaduan-monthly')->monthlyOn(1, '8.30');

        $schedule->command('update-sla')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('check-case-status-task-missing')->everyFiveMinutes();
        $schedule->command('auto-close-case')->dailyAt('06:05');
        $schedule->command('crm-cases-cs-response')->dailyAt('08:32');
        $schedule->command('cs-average-monthly')->monthlyOn(1, '08:40');
        $schedule->command('check-email-inbound')->everyMinute();
        $schedule->command('checking-duplicate-case')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        // $schedule->command('update-sla-flag3-task')->everyFiveMinutes();
        $schedule->command('update-cs-sla-task')->everyFiveMinutes()->unlessBetween('04:00', '06:00'); //->between('08:00', '18:00');
        $schedule->command('case-detail')->dailyAt('06:20');
        $schedule->command('case-statistic-performance-agent')->dailyAt('08:30');
        // $schedule->command('crm-blast-email')->hourly();
        $schedule->command('crm-cs-response-daily')->dailyAt('18:10');
        $schedule->command('daily-collection-cdci-report')->dailyAt('06:30');
        $schedule->command('top-supplier-casemonitoring')->dailyAt('18:00');
        $schedule->command('case-statistic-performance-outbound')->dailyAt('08:30');
        $schedule->command('lms-cert-gen')->dailyAt('09:00');
        $schedule->command('lms-note-gen')->dailyAt('18:00');
        $schedule->command('check-case-status')->hourly();
        $schedule->command('update-sla-flag3-task')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('auto-ack-severity')->everyFiveMinutes()->unlessBetween('04:00', '06:00'); //->between('08:00', '21:00');
        $schedule->command('auto-update-cases-severity')->twiceDaily(15, 22);
        $schedule->command('auto-create-task-rit')->everyTenMinutes(); //->between('08:00', '21:00');
        $schedule->command('case-detail-onsite-support-daily')->dailyAt('9:00');
        $schedule->command('case-detail-onsite-support-today')->dailyAt('17:00');
        $schedule->command('auto-check-execution-date-callin')->hourly();
        $schedule->command('check-spam-email-crm-ep')->everyMinute();
        
        $schedule->command('incident-case-ageing-specialist')->dailyAt('08:01');
        $schedule->command('auto-check-bug-cases')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('checking-invalid-case')->everyFiveMinutes()->unlessBetween('04:00', '06:00');
        $schedule->command('case-open-task-completed')->dailyAt('10:00');
        $schedule->command('auto-approval-s4')->hourly()->unlessBetween('04:00', '06:00');
        $schedule->command('pom-sla-management')->dailyAt('9:30');
//        $schedule->command('case-detail-all-adhoc')->tuesdays()->at("17:40");
        $schedule->command('auto-fill-category-cancelled-eaduan')->dailyAt('06:45');

        /*
         * CASB Report
         */
        $schedule->command('case-detail-mytv-altel-today')->dailyAt('08:10');
        $schedule->command('case-detail-mytv-altel-weekly')->weeklyOn(1, '8:15'); //run every week on monday
        $schedule->command('case-detail-mytv-altel-monthly')->monthlyOn(1, '8:30');//run monthly on first day
        
        /*
         * Crm Ssm Report
         */
        // $schedule->command('case-detail-crm-ssm-today')->dailyAt('02:00');
        //$schedule->command('case-detail-crm-ssm-weekly')->weeklyOn(1, '05:30'); //run every week on monday
        // $schedule->command('case-detail-crm-ssm-monthly')->monthlyOn(1, '17:30');//run monthly on first day
        // $schedule->command('check-inbound-email-ssm')->everyFiveMinutes();
        // $schedule->command('open-case-detail-crm-ssm')->dailyAt('03:00');
        // $schedule->command('daily-open-case-detail-crm-ssm')->dailyAt('13:00');
//        $schedule->command('case-detail-crm-jbal-weekly')->weeklyOn(1, '07:30');//run monthly on first day
//        $schedule->command('case-detail-crm-jbal-daily')->dailyAt('07:15');
//        $schedule->command('case-detail-crm-ssm-weekly')->wednesdays()->at("17:00");

        /*
        * CRM Gamuda Report
        */
        // $schedule->command('case-detail-crm-gamuda-today')->dailyAt('06:30');
        // $schedule->command('case-detail-crm-gamuda-today')->everyFiveMinutes();
        
        /*
         * Crm Ssm Bugs
         */
        $schedule->command('delete-invalid-case')->everyFiveMinutes();
        $schedule->command('update-inbound-ssm')->everyMinute();
        $schedule->command('duplicate-case-ssm')->everyFiveMinutes();
        $schedule->command('delete-emptydesc-case-ssm')->everyFiveMinutes();
        $schedule->command('missing-task')->hourly();
        $schedule->command('open-invalid-case')->everyFiveMinutes();
        
        /*
         * Re-run command for error send email in report
         */
//        $schedule->command('report-monitoring')->everyTenMinutes();
    }

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        require base_path('routes/console.php');
    }
}







